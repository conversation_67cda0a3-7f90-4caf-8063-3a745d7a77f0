# ResUNet Small - Optimized Architecture for Spheroid Segmentation

## Overview

ResUNet Small je nová architektura navržená specificky pro segmentaci sféroidů s rozlišením 1024x1024. Model má přibližně **58M parametrů** a je optimalizován pro lepší generalizaci pomocí pokročilých regularizačních technik.

## Klíčové vlastnosti

### 🎯 **Optimalizovaná kapacita**
- **58,060,957 parametrů** (~58M) - ideální pro 1024x1024 rozlišení
- **221.5 MB** velikost modelu
- Vyšší batch size možný díky menší velikosti

### 🔧 **Pokročilé regularizační techniky**
- **Spatial Dropout**: `nn.Dropout2d` pro lepší prostorovou regularizaci
- **Progressive Dropout**: Různé míry dropout pro růz<PERSON><PERSON> v<PERSON> (0.075, 0.15)
- **Enhanced SE Blocks**: Squeeze-and-Excitation s dropout
- **Spatial Attention**: Dodatečná prostorová pozornost

### 🏗️ **Architektonické vylepšení**
- **Enhanced Residual Blocks**: Vylepšené reziduální bloky s více regularizací
- **Enhanced Attention Gates**: Pokročilé attention gates s prostorovou pozorností
- **Deep Supervision**: Volitelná multi-scale supervize
- **Proper Weight Initialization**: He inicializace pro stabilní trénování

## Architektura

```
Input (3, 1024, 1024)
    ↓
Initial Conv (7x7) + Norm + ReLU + Dropout
    ↓
Encoder: [48, 96, 192, 384, 512] channels
    ↓ (Enhanced Residual Blocks + MaxPool)
Bottleneck: 512 → 1024 channels (2x Enhanced Residual Blocks)
    ↓
Decoder: [512, 384, 192, 96, 48] channels
    ↓ (ConvTranspose + Enhanced Attention Gates + Enhanced Residual Blocks)
Final Conv: 48 → 24 → 1 channel
    ↓
Output (1, 1024, 1024)
```

## Použití

### Základní použití
```python
from models.resunet_small import ResUNetSmall

# Vytvoření modelu
model = ResUNetSmall(
    in_channels=3,
    out_channels=1,
    dropout_rate=0.15,
    use_instance_norm=True
)

# Forward pass
x = torch.randn(1, 3, 1024, 1024)
output = model(x)  # Shape: [1, 1, 1024, 1024]
```

### S deep supervision
```python
model = ResUNetSmall(
    in_channels=3,
    out_channels=1,
    dropout_rate=0.15,
    use_deep_supervision=True
)

# Během trénování
model.train()
x = torch.randn(1, 3, 1024, 1024)
main_output, deep_outputs = model(x)

# Během inference
model.eval()
output = model(x)  # Pouze hlavní výstup
```

## Trénování

### Pretraining
```bash
# Spuštění pretrainingu
cd scripts/training
./pretrain_resunet_small.sh
```

**Parametry pretrainingu:**
- Batch size: 12 (per GPU)
- Learning rate: 2e-4
- Epochs: 150
- Optimizer: AdamW
- Scheduler: Cosine
- Weight decay: 1e-4

### Finetuning
```bash
# Spuštění finetuning s automatickou detekcí pretrained modelu
./finetune_resunet_small.sh

# Nebo s konkrétním pretrained modelem
./finetune_resunet_small.sh --pretrained_path path/to/pretrained/model.pth
```

**Parametry finetuning:**
- Batch size: 16 (per GPU)
- Learning rate: 5e-5
- Epochs: 100
- Freeze backbone: 10 epochs
- Optimizer: AdamW

## Výkonnostní charakteristiky

### Paměťové nároky (1024x1024)
| Batch Size | GPU Memory | Status |
|------------|------------|---------|
| 1          | ~13 MB     | ✅      |
| 2          | ~4 MB      | ✅      |
| 4          | ~8 MB      | ✅      |
| 8          | ~16 MB     | ✅      |
| 12         | ~16 MB     | ✅      |
| 16         | ~20 MB     | ✅      |

### Porovnání s ostatními modely
| Model | Parametry | Velikost | 1024x1024 | Batch Size |
|-------|-----------|----------|-----------|------------|
| ResUNet Original | 14.5M | 55.4 MB | ✅ | 6-8 |
| ResUNet Small | **58.1M** | **221.5 MB** | ✅ | **12-16** |
| ResUNet Optimized | 2.4M | 9.0 MB | ✅ | 16+ |

## Regularizační techniky

### 1. Spatial Dropout
```python
# Místo standardního dropout
nn.Dropout2d(p=0.15)  # Vypíná celé feature mapy
```

### 2. Progressive Dropout
```python
# Různé míry pro různé vrstvy
dropout1 = nn.Dropout2d(p=dropout * 0.5)  # 0.075
dropout2 = nn.Dropout2d(p=dropout)        # 0.15
```

### 3. Enhanced SE Blocks
```python
# SE block s dropout pro regularizaci
self.fc = nn.Sequential(
    nn.Linear(in_channels, reduced_channels),
    nn.ReLU(inplace=True),
    nn.Dropout(p=dropout),  # Dodatečná regularizace
    nn.Linear(reduced_channels, in_channels),
    nn.Sigmoid()
)
```

### 4. Spatial Attention
```python
# Prostorová pozornost pro lepší focus
avg_out = torch.mean(x, dim=1, keepdim=True)
max_out, _ = torch.max(x, dim=1, keepdim=True)
attention = torch.cat([avg_out, max_out], dim=1)
```

## Testování

```bash
# Test modelu a parametrů
python test_resunet_small.py
```

Test ověří:
- ✅ Počet parametrů (~58M)
- ✅ Forward pass s různými velikostmi
- ✅ Kompatibilitu s 1024x1024
- ✅ Paměťové nároky
- ✅ Porovnání s ostatními modely

## Doporučené nastavení

### Pro pretraining
```python
# CNN_main_spheroid.py argumenty
--model resunet_small
--batch_size 12
--lr 2e-4
--epochs 150
--optimizer adamw
--scheduler cosine
--weight_decay 1e-4
--use_instance_norm
--use_tta
```

### Pro finetuning
```python
# CNN_main_spheroid.py argumenty
--model resunet_small
--pretrained_path path/to/pretrained.pth
--batch_size 16
--lr 5e-5
--epochs 100
--freeze_backbone_epochs 10
--optimizer adamw
--scheduler cosine
```

## Výhody oproti původnímu ResUNet

1. **Vyšší kapacita**: 58M vs 14.5M parametrů
2. **Lepší regularizace**: Spatial dropout, progressive dropout
3. **Pokročilá pozornost**: Enhanced attention gates + spatial attention
4. **Vyšší batch size**: 12-16 vs 6-8
5. **Stabilnější trénování**: Lepší inicializace a gradient flow
6. **Flexibilní konfigurace**: Deep supervision, různé dropout módy

## Soubory

- `models/resunet_small.py` - Hlavní implementace modelu
- `scripts/training/pretrain_resunet_small.sh` - Pretraining skript
- `scripts/training/finetune_resunet_small.sh` - Finetuning skript
- `test_resunet_small.py` - Test skript
- `RESUNET_SMALL_README.md` - Tato dokumentace

## Troubleshooting

### Paměťové problémy
```bash
# Snižte batch size
--batch_size 8

# Nebo použijte gradient checkpointing
--use_checkpoint
```

### Pomalé trénování
```bash
# Zvyšte počet workers
--num_workers 8

# Použijte cache
--use_cache
```

### Nestabilní trénování
```bash
# Snižte learning rate
--lr 1e-4

# Zvyšte gradient clipping
--gradient_clip_val 0.5
```
