#!/usr/bin/env python3
"""
Evaluační skript optimalizovaný pro server s GPU
Testuje natrénované modely na test setu: /data/prusek/training_small/test
"""

import os
import sys
import time
import argparse
import json
from pathlib import Path
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from PIL import Image
import cv2
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend pro server
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# Import model architektur
sys.path.append('/home/<USER>/SpheroSeg/NN/diplomka')
from models.resunet import ResUNet
from models.hrnet import HRNetV2
from models.pspnet_stable import PSPNet
from models.transunet import TransUNet
from models.resunet_advanced import AdvancedResUNet

class SegmentationDataset(torch.utils.data.Dataset):
    """Dataset pro načítání obr<PERSON>zků a masek"""
    
    def __init__(self, images_dir, masks_dir, transform=None):
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.transform = transform
        
        # Najdi všechny obrázky (vyfiltruj skryté soubory)
        image_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
        self.image_files = []
        
        for ext in image_extensions:
            files = list(self.images_dir.glob(f'*{ext}')) + list(self.images_dir.glob(f'*{ext.upper()}'))
            # Filtruj skryté soubory (začínající tečkou nebo ._)
            files = [f for f in files if not f.name.startswith('.') and not f.name.startswith('._')]
            self.image_files.extend(files)
        
        # Najdi odpovídající masky
        self.valid_pairs = []
        for img_file in self.image_files:
            mask_file = self.find_mask_file(img_file)
            if mask_file and mask_file.exists():
                self.valid_pairs.append((img_file, mask_file))
        
        print(f"Nalezeno {len(self.valid_pairs)} platných párů obrázek-maska")
    
    def find_mask_file(self, img_file):
        """Najdi odpovídající soubor masky"""
        base_name = img_file.stem
        mask_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
        
        for ext in mask_extensions:
            mask_file = self.masks_dir / f"{base_name}{ext}"
            if mask_file.exists():
                return mask_file
            mask_file = self.masks_dir / f"{base_name}{ext.upper()}"
            if mask_file.exists():
                return mask_file
        return None
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        img_path, mask_path = self.valid_pairs[idx]
        
        # Načti obrázek
        image = Image.open(img_path).convert('RGB')
        image = np.array(image)
        
        # Načti masku
        mask = Image.open(mask_path).convert('L')
        mask = np.array(mask)
        
        # Normalizuj masku na 0-1
        mask = (mask > 128).astype(np.float32)
        
        # Resize na 1024x1024
        image = cv2.resize(image, (1024, 1024))
        mask = cv2.resize(mask, (1024, 1024))
        
        # Převeď na tensor
        image = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        mask = torch.from_numpy(mask).float()
        
        return image, mask, str(img_path.name)

def load_model(model_name, model_path, device):
    """Načti model podle typu"""
    print(f"  Načítám model {model_name}...")

    if model_name == 'resunet':
        model = ResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
    elif model_name == 'hrnet':
        model = HRNetV2(n_class=1, use_instance_norm=True)
    elif model_name == 'pspnet':
        model = PSPNet(n_class=1, use_instance_norm=True)
    elif model_name == 'transunet':
        model = TransUNet(in_channels=3, out_channels=1, img_size=1024, use_instance_norm=True)
    elif model_name == 'resunet_advanced':
        model = AdvancedResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
    else:
        raise ValueError(f"Neznámý model: {model_name}")

    # Spočítej počet parametrů
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"  Celkový počet parametrů: {total_params:,}")
    print(f"  Trénovatelné parametry: {trainable_params:,}")

    # Načti váhy
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        if 'epoch' in checkpoint:
            print(f"  Model natrénován {checkpoint['epoch']} epoch")
        if 'best_val_loss' in checkpoint:
            print(f"  Nejlepší validační loss: {checkpoint['best_val_loss']:.4f}")
    else:
        model.load_state_dict(checkpoint)

    model.to(device)
    model.eval()

    # Ověř, že model je v eval módu
    for module in model.modules():
        if hasattr(module, 'training'):
            assert not module.training, f"Modul {module} není v eval módu!"

    print(f"  Model {model_name} úspěšně načten")
    return model

def calculate_metrics(pred_mask, true_mask, threshold=0.5):
    """Vypočítej segmentační metriky"""
    pred_binary = (pred_mask > threshold).astype(np.uint8).flatten()
    true_binary = true_mask.astype(np.uint8).flatten()
    
    # Základní metriky
    accuracy = accuracy_score(true_binary, pred_binary)
    precision = precision_score(true_binary, pred_binary, zero_division=0)
    recall = recall_score(true_binary, pred_binary, zero_division=0)
    f1 = f1_score(true_binary, pred_binary, zero_division=0)
    
    # IoU (Intersection over Union)
    intersection = np.logical_and(pred_binary, true_binary).sum()
    union = np.logical_or(pred_binary, true_binary).sum()
    iou = intersection / union if union > 0 else 0
    
    # Dice coefficient
    dice = 2 * intersection / (pred_binary.sum() + true_binary.sum()) if (pred_binary.sum() + true_binary.sum()) > 0 else 0
    
    # Specificity (True Negative Rate)
    tn = np.logical_and(pred_binary == 0, true_binary == 0).sum()
    fp = np.logical_and(pred_binary == 1, true_binary == 0).sum()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'iou': iou,
        'dice': dice,
        'specificity': specificity
    }

def get_fixed_threshold():
    """Vrátí fixní threshold 0.55 pro spravedlivé porovnání všech modelů"""
    threshold = 0.55
    print(f"Používám fixní threshold: {threshold} (stejný pro všechny modely)")
    return threshold, []

def evaluate_model(model, dataloader, device, model_name):
    """Evaluuj model na celém datasetu"""
    all_metrics = []
    inference_times = []

    print(f"Evaluuji model {model_name}...")

    # Warm-up run pro stabilní měření času
    print("  Provádím warm-up...")
    with torch.no_grad():
        dummy_input = torch.randn(1, 3, 1024, 1024).to(device)
        for _ in range(3):
            _ = model(dummy_input)
            if device.type == 'cuda':
                torch.cuda.synchronize()

    with torch.no_grad():
        for batch_idx, (images, masks, filenames) in enumerate(dataloader):
            images = images.to(device)
            masks = masks.to(device)

            # Měř čas inference pro každý obrázek v batchi zvlášť pro spravedlivé porovnání
            batch_inference_times = []
            batch_outputs = []

            for i in range(images.shape[0]):
                single_image = images[i:i+1]  # Zachovej batch dimenzi

                # Synchronizace před měřením
                if device.type == 'cuda':
                    torch.cuda.synchronize()

                start_time = time.time()
                output = model(single_image)

                # Synchronizace po inference
                if device.type == 'cuda':
                    torch.cuda.synchronize()

                inference_time = time.time() - start_time

                if isinstance(output, tuple):  # TransUNet returns tuple
                    output = output[0]

                batch_outputs.append(output)
                batch_inference_times.append(inference_time)

            # Spojit výstupy zpět do batche
            outputs = torch.cat(batch_outputs, dim=0)

            # Aplikuj sigmoid
            outputs = torch.sigmoid(outputs)

            # Převeď na numpy
            pred_masks = outputs.cpu().numpy()
            true_masks = masks.cpu().numpy()

            # Vypočítej metriky pro každý obrázek v batchi s fixním threshold
            for i in range(pred_masks.shape[0]):
                metrics = calculate_metrics(pred_masks[i, 0], true_masks[i], threshold=0.55)
                metrics['filename'] = filenames[i]
                metrics['inference_time'] = batch_inference_times[i]
                all_metrics.append(metrics)
                inference_times.append(batch_inference_times[i])

            if (batch_idx + 1) % 10 == 0:
                print(f"  Zpracováno {batch_idx + 1}/{len(dataloader)} batchů")

    return all_metrics, inference_times

def save_results_simple(results, output_dir):
    """Ulož výsledky do souborů"""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Ulož detailed results jako JSON
    with open(output_dir / 'detailed_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Vytvoř summary tabulku
    summary_data = []
    for model_name, data in results.items():
        if model_name == 'dataset_info':
            continue
            
        metrics = data['metrics']
        avg_metrics = {
            'Model': model_name,
            'Accuracy': np.mean([m['accuracy'] for m in metrics]),
            'Precision': np.mean([m['precision'] for m in metrics]),
            'Recall': np.mean([m['recall'] for m in metrics]),
            'F1-Score': np.mean([m['f1_score'] for m in metrics]),
            'IoU': np.mean([m['iou'] for m in metrics]),
            'Dice': np.mean([m['dice'] for m in metrics]),
            'Specificity': np.mean([m['specificity'] for m in metrics]),
            'Avg_Inference_Time': np.mean(data['inference_times']),
            'Std_Inference_Time': np.std(data['inference_times']),
            'Optimal_Threshold': data['optimal_threshold']
        }
        summary_data.append(avg_metrics)
    
    # Ulož jako CSV
    df = pd.DataFrame(summary_data)
    df.to_csv(output_dir / 'model_comparison.csv', index=False)
    
    print(f"Výsledky uloženy do: {output_dir}")
    print("\nSouhrn výsledků:")
    print(df.to_string(index=False))

def main():
    # Hardcoded cesty pro server
    dataset_path = Path("/data/prusek/training_small/test")
    models_path = Path("/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/outputs")
    output_dir = f"evaluation_results_{time.strftime('%Y%m%d_%H%M%S')}"
    
    print(f"Dataset: {dataset_path}")
    print(f"Modely: {models_path}")
    print(f"Výstup: {output_dir}")
    
    # Zkontroluj cesty
    if not dataset_path.exists():
        print(f"Dataset path neexistuje: {dataset_path}")
        return
    
    if not models_path.exists():
        print(f"Models path neexistuje: {models_path}")
        return
    
    images_dir = dataset_path / 'images'
    masks_dir = dataset_path / 'masks'
    
    if not images_dir.exists() or not masks_dir.exists():
        print(f"Složky images nebo masks neexistují v {dataset_path}")
        return
    
    # Nastavení zařízení
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Používám zařízení: {device}")
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"CUDA verze: {torch.version.cuda}")
        print(f"PyTorch verze: {torch.__version__}")
        # Vyčisti GPU cache před evaluací
        torch.cuda.empty_cache()
    
    # Vytvoř dataset
    dataset = SegmentationDataset(images_dir, masks_dir)
    # Použij menší batch size pro spravedlivé měření času (každý obrázek se měří zvlášť)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=False, num_workers=2, pin_memory=True)
    
    # Najdi všechny natrénované modely
    model_configs = [
        ('resunet', 'resunet_finetuned'),
        ('hrnet', 'hrnet_finetuned'),
        ('pspnet', 'pspnet_finetuned'),
        ('resunet_advanced', 'resunet_advanced_finetuned')
    ]
    
    results = {
        'dataset_info': {
            'path': str(dataset_path),
            'num_images': len(dataset),
            'device': str(device)
        }
    }
    
    # Evaluuj každý model
    for model_name, folder_name in model_configs:
        model_folder = models_path / folder_name
        model_file = model_folder / 'best_model.pth'
        
        if not model_file.exists():
            print(f"Model {model_name} nenalezen: {model_file}")
            continue
        
        print(f"\n{'='*50}")
        print(f"Evaluuji model: {model_name}")
        print(f"Model cesta: {model_file}")
        
        try:
            # Načti model
            model = load_model(model_name, model_file, device)
            
            # Použij fixní threshold
            optimal_threshold, threshold_data = get_fixed_threshold()
            print(f"Použitý threshold: {optimal_threshold:.3f}")
            
            # Evaluuj model s optimálním threshold
            metrics, inference_times = evaluate_model(model, dataloader, device, model_name)
            
            # Uložení výsledků
            results[model_name] = {
                'model_path': str(model_file),
                'optimal_threshold': 0.55,
                'threshold_data': [],
                'metrics': metrics,
                'inference_times': inference_times,
                'avg_metrics': {
                    'accuracy': np.mean([m['accuracy'] for m in metrics]),
                    'precision': np.mean([m['precision'] for m in metrics]),
                    'recall': np.mean([m['recall'] for m in metrics]),
                    'f1_score': np.mean([m['f1_score'] for m in metrics]),
                    'iou': np.mean([m['iou'] for m in metrics]),
                    'dice': np.mean([m['dice'] for m in metrics]),
                    'specificity': np.mean([m['specificity'] for m in metrics]),
                },
                'time_stats': {
                    'avg_inference_time': np.mean(inference_times),
                    'std_inference_time': np.std(inference_times),
                    'min_inference_time': np.min(inference_times),
                    'max_inference_time': np.max(inference_times)
                }
            }
            
            print(f"Průměrné metriky pro {model_name}:")
            for metric, value in results[model_name]['avg_metrics'].items():
                print(f"  {metric}: {value:.4f}")
            print(f"  Průměrný čas inference: {np.mean(inference_times)*1000:.2f} ± {np.std(inference_times)*1000:.2f} ms")
            print(f"  Min/Max čas inference: {np.min(inference_times)*1000:.2f}/{np.max(inference_times)*1000:.2f} ms")
            print(f"  Celkový počet testovacích obrázků: {len(metrics)}")
            
        except Exception as e:
            print(f"Chyba při evaluaci {model_name}: {e}")
            continue
    
    # Ulož výsledky
    save_results_simple(results, output_dir)
    
    print(f"\n{'='*50}")
    print("Evaluace dokončena!")
    print(f"Výsledky uloženy do: {output_dir}")

if __name__ == '__main__':
    main()