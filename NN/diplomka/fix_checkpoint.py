#!/usr/bin/env python3
"""
Checkpoint repair utility for PyTorch 2.6+ compatibility
Fixes weights_only loading issues and cleans problematic checkpoint data
"""

import torch
import argparse
import os
from pathlib import Path
import sys

def clean_checkpoint(checkpoint_path, output_path=None):
    """
    Clean checkpoint to make it compatible with weights_only=True loading
    
    Args:
        checkpoint_path: Path to the problematic checkpoint
        output_path: Path to save the cleaned checkpoint (optional)
    """
    print(f"Loading checkpoint from: {checkpoint_path}")
    
    try:
        # Try loading with different methods
        checkpoint = None
        
        # Method 1: Try weights_only=False first
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            print("✓ Loaded with weights_only=False")
        except Exception as e:
            print(f"✗ weights_only=False failed: {e}")
            
            # Method 2: Try without weights_only parameter
            try:
                checkpoint = torch.load(checkpoint_path, map_location='cpu')
                print("✓ Loaded without weights_only parameter")
            except Exception as e2:
                print(f"✗ Legacy loading failed: {e2}")
                return False
        
        if checkpoint is None:
            print("✗ Failed to load checkpoint with any method")
            return False
        
        print(f"Original checkpoint keys: {list(checkpoint.keys())}")
        
        # Create cleaned checkpoint with only essential data
        cleaned_checkpoint = {}
        
        # Essential keys to preserve
        essential_keys = [
            'model_state_dict',
            'optimizer_state_dict', 
            'epoch',
            'best_iou',
            'best_dice',
            'train_loss',
            'val_loss',
            'learning_rate'
        ]
        
        # Copy essential data
        for key in essential_keys:
            if key in checkpoint:
                if key == 'model_state_dict':
                    # Clean model state dict
                    model_state = checkpoint[key]
                    cleaned_model_state = {}
                    
                    for param_name, param_value in model_state.items():
                        if isinstance(param_value, torch.Tensor):
                            cleaned_model_state[param_name] = param_value.clone()
                        else:
                            print(f"Warning: Skipping non-tensor parameter: {param_name}")
                    
                    cleaned_checkpoint[key] = cleaned_model_state
                    print(f"✓ Cleaned model_state_dict with {len(cleaned_model_state)} parameters")
                    
                elif key == 'optimizer_state_dict':
                    # Clean optimizer state dict
                    try:
                        opt_state = checkpoint[key]
                        if isinstance(opt_state, dict):
                            cleaned_checkpoint[key] = opt_state
                            print(f"✓ Preserved optimizer_state_dict")
                        else:
                            print(f"Warning: Skipping problematic optimizer state")
                    except Exception as e:
                        print(f"Warning: Could not clean optimizer state: {e}")
                        
                else:
                    # Copy other essential data
                    try:
                        cleaned_checkpoint[key] = checkpoint[key]
                        print(f"✓ Preserved {key}: {checkpoint[key]}")
                    except Exception as e:
                        print(f"Warning: Could not preserve {key}: {e}")
        
        # Set output path
        if output_path is None:
            base_path = Path(checkpoint_path)
            output_path = base_path.parent / f"{base_path.stem}_cleaned{base_path.suffix}"
        
        # Save cleaned checkpoint
        print(f"\nSaving cleaned checkpoint to: {output_path}")
        torch.save(cleaned_checkpoint, output_path)
        
        # Verify the cleaned checkpoint can be loaded
        print("Verifying cleaned checkpoint...")
        try:
            test_load = torch.load(output_path, map_location='cpu', weights_only=True)
            print("✓ Cleaned checkpoint can be loaded with weights_only=True")
            
            # Check model state dict
            if 'model_state_dict' in test_load:
                model_params = len(test_load['model_state_dict'])
                print(f"✓ Model state dict contains {model_params} parameters")
            
            return True
            
        except Exception as e:
            print(f"✗ Verification failed: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to clean checkpoint: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Fix PyTorch checkpoint compatibility issues')
    parser.add_argument('checkpoint_path', type=str, help='Path to the problematic checkpoint')
    parser.add_argument('--output_path', type=str, default=None, 
                       help='Output path for cleaned checkpoint (default: adds _cleaned suffix)')
    parser.add_argument('--verify_only', action='store_true',
                       help='Only verify if checkpoint can be loaded, do not create cleaned version')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.checkpoint_path):
        print(f"Error: Checkpoint file not found: {args.checkpoint_path}")
        sys.exit(1)
    
    print("=" * 60)
    print("PyTorch Checkpoint Repair Utility")
    print("=" * 60)
    
    if args.verify_only:
        print("Verification mode - checking if checkpoint can be loaded...")
        
        # Test different loading methods
        methods = [
            ("weights_only=True", lambda p: torch.load(p, map_location='cpu', weights_only=True)),
            ("weights_only=False", lambda p: torch.load(p, map_location='cpu', weights_only=False)),
            ("Legacy mode", lambda p: torch.load(p, map_location='cpu'))
        ]
        
        for method_name, load_func in methods:
            try:
                checkpoint = load_func(args.checkpoint_path)
                print(f"✓ {method_name}: SUCCESS")
                if 'model_state_dict' in checkpoint:
                    print(f"  - Model parameters: {len(checkpoint['model_state_dict'])}")
                if 'best_iou' in checkpoint:
                    print(f"  - Best IoU: {checkpoint['best_iou']:.4f}")
            except Exception as e:
                print(f"✗ {method_name}: FAILED - {e}")
    else:
        success = clean_checkpoint(args.checkpoint_path, args.output_path)
        
        if success:
            print("\n" + "=" * 60)
            print("✓ Checkpoint successfully cleaned!")
            print("=" * 60)
            print("\nYou can now use the cleaned checkpoint for finetuning:")
            output_path = args.output_path or str(Path(args.checkpoint_path).parent / f"{Path(args.checkpoint_path).stem}_cleaned{Path(args.checkpoint_path).suffix}")
            print(f"./finetune_resunet_small.sh --pretrained_path {output_path}")
        else:
            print("\n" + "=" * 60)
            print("✗ Failed to clean checkpoint")
            print("=" * 60)
            sys.exit(1)

if __name__ == "__main__":
    main()
