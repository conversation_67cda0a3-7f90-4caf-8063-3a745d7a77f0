# Optimized ResUNet for limited GPU memory
# Maintains advanced features but with reduced memory footprint

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


# ===========================
# Normalization Helper
# ===========================
def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer (Instance or Batch)"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


# ===========================
# Efficient Triplet Attention (Memory Optimized)
# ===========================
class EfficientTripletAttention(nn.Module):
    """
    Memory-efficient version of Triplet Attention
    Uses depthwise separable convolutions
    """
    def __init__(self, channels, kernel_size=7):
        super(EfficientTripletAttention, self).__init__()
        self.kernel_size = kernel_size
        padding = (kernel_size - 1) // 2
        
        # Use depthwise separable convolutions for efficiency
        self.conv_spatial = nn.Sequential(
            nn.Conv2d(2, 2, kernel_size=kernel_size, padding=padding, groups=2, bias=False),
            nn.Conv2d(2, 1, kernel_size=1, bias=False),
            nn.Sigmoid()
        )
        
        # Channel attention with reduced dimensions
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // 16, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 16, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # Spatial attention (memory efficient)
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial = torch.cat([avg_out, max_out], dim=1)
        spatial = self.conv_spatial(spatial)
        
        # Channel attention
        avg_pool = self.fc(self.avg_pool(x))
        max_pool = self.fc(self.max_pool(x))
        channel = avg_pool + max_pool
        
        return x * spatial * channel


# ===========================
# Optimized Residual Block
# ===========================
class OptimizedResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1, use_attention=True, 
                 dropout=0.1, use_instance_norm=True):
        super(OptimizedResidualBlock, self).__init__()
        
        # Main convolution path
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.norm1 = get_norm_layer(out_channels, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              padding=1, bias=False)
        self.norm2 = get_norm_layer(out_channels, use_instance_norm)
        
        # Dropout for regularization
        self.dropout = nn.Dropout2d(p=dropout) if dropout > 0 else nn.Identity()
        
        # Single efficient attention mechanism
        if use_attention:
            self.attention = EfficientTripletAttention(out_channels)
        else:
            self.attention = nn.Identity()
        
        # Skip connection
        self.skip = nn.Identity()
        if stride != 1 or in_channels != out_channels:
            self.skip = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                get_norm_layer(out_channels, use_instance_norm)
            )
            
    def forward(self, x):
        identity = self.skip(x)
        
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu(out)
        out = self.dropout(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        
        # Apply attention before adding residual
        out = self.attention(out)
        
        out += identity
        out = self.relu(out)
        
        return out


# ===========================
# Simple Attention Gate
# ===========================
class SimpleAttentionGate(nn.Module):
    """Simplified attention gate for memory efficiency"""
    def __init__(self, F_g, F_l, F_int, use_instance_norm=True):
        super(SimpleAttentionGate, self).__init__()
        
        self.W_g = nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=False)
        self.W_x = nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=False)
        
        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            nn.Sigmoid()
        )
        
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        return x * psi


# ===========================
# Optimized ResUNet
# ===========================
class OptimizedResUNet(nn.Module):
    """
    Memory-optimized ResUNet with modern attention mechanisms
    Designed for training on GPUs with limited memory
    """
    def __init__(self, in_channels=3, out_channels=1, 
                 features=[48, 96, 192, 384],  # Reduced for memory
                 use_instance_norm=True, use_checkpoint=False):
        super(OptimizedResUNet, self).__init__()
        
        self.use_instance_norm = use_instance_norm
        self.use_checkpoint = use_checkpoint
        
        # Initial convolution
        self.init_conv = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=3, stride=1, padding=1, bias=False),
            get_norm_layer(features[0], use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
        # Encoder
        self.encoder1 = OptimizedResidualBlock(features[0], features[0], use_instance_norm=use_instance_norm)
        self.pool1 = nn.MaxPool2d(2)
        
        self.encoder2 = OptimizedResidualBlock(features[0], features[1], use_instance_norm=use_instance_norm)
        self.pool2 = nn.MaxPool2d(2)
        
        self.encoder3 = OptimizedResidualBlock(features[1], features[2], use_instance_norm=use_instance_norm)
        self.pool3 = nn.MaxPool2d(2)
        
        self.encoder4 = OptimizedResidualBlock(features[2], features[3], use_instance_norm=use_instance_norm)
        self.pool4 = nn.MaxPool2d(2)
        
        # Bottleneck - reduced expansion factor
        bottleneck_channels = int(features[3] * 1.5)  # 1.5x instead of 2x
        self.bottleneck = nn.Sequential(
            OptimizedResidualBlock(features[3], bottleneck_channels, use_instance_norm=use_instance_norm),
            OptimizedResidualBlock(bottleneck_channels, bottleneck_channels, use_instance_norm=use_instance_norm)
        )
        
        # Decoder with attention gates
        self.upconv4 = nn.ConvTranspose2d(bottleneck_channels, features[3], kernel_size=2, stride=2)
        self.att4 = SimpleAttentionGate(features[3], features[3], features[3]//2, use_instance_norm)
        self.decoder4 = OptimizedResidualBlock(features[3]*2, features[3], use_instance_norm=use_instance_norm)
        
        self.upconv3 = nn.ConvTranspose2d(features[3], features[2], kernel_size=2, stride=2)
        self.att3 = SimpleAttentionGate(features[2], features[2], features[2]//2, use_instance_norm)
        self.decoder3 = OptimizedResidualBlock(features[2]*2, features[2], use_instance_norm=use_instance_norm)
        
        self.upconv2 = nn.ConvTranspose2d(features[2], features[1], kernel_size=2, stride=2)
        self.att2 = SimpleAttentionGate(features[1], features[1], features[1]//2, use_instance_norm)
        self.decoder2 = OptimizedResidualBlock(features[1]*2, features[1], use_instance_norm=use_instance_norm)
        
        self.upconv1 = nn.ConvTranspose2d(features[1], features[0], kernel_size=2, stride=2)
        self.att1 = SimpleAttentionGate(features[0], features[0], features[0]//2, use_instance_norm)
        self.decoder1 = OptimizedResidualBlock(features[0]*2, features[0], use_instance_norm=use_instance_norm)
        
        # Final output
        self.final_conv = nn.Conv2d(features[0], out_channels, kernel_size=1)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
                    
    def forward(self, x):
        # Encoder
        x = self.init_conv(x)
        
        e1 = self.encoder1(x)
        e2 = self.encoder2(self.pool1(e1))
        e3 = self.encoder3(self.pool2(e2))
        e4 = self.encoder4(self.pool3(e3))
        
        # Bottleneck
        b = self.bottleneck(self.pool4(e4))
        
        # Decoder with attention gates
        d4 = self.upconv4(b)
        d4 = torch.cat([self.att4(d4, e4), d4], dim=1)
        d4 = self.decoder4(d4)
        
        d3 = self.upconv3(d4)
        d3 = torch.cat([self.att3(d3, e3), d3], dim=1)
        d3 = self.decoder3(d3)
        
        d2 = self.upconv2(d3)
        d2 = torch.cat([self.att2(d2, e2), d2], dim=1)
        d2 = self.decoder2(d2)
        
        d1 = self.upconv1(d2)
        d1 = torch.cat([self.att1(d1, e1), d1], dim=1)
        d1 = self.decoder1(d1)
        
        # Final output
        out = self.final_conv(d1)
        
        return out


# Alias for compatibility
ResUNet = OptimizedResUNet