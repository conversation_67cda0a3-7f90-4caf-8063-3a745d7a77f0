# evaluate_model.py - Evaluation script for trained models

import torch
import torch.nn as nn
import numpy as np
import cv2 as cv
from pathlib import Path
import argparse
from tqdm import tqdm
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import albumentations as A
from albumentations.pytorch import ToTensorV2

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from CNN_main_spheroid import SpheroidDataset, get_validation_augmentation, apply_tta
from models.resunet import ResUNet
from models.hrnet import HRNetV2
from models.pspnet import PSPNet
from models.transunet import TransUNet


def get_model(model_name, num_classes=1, img_size=1024, use_instance_norm=True):
    """Load model architecture"""
    if model_name.lower() == 'resunet':
        return ResUNet(in_channels=3, out_channels=num_classes, use_instance_norm=use_instance_norm)
    elif model_name.lower() == 'hrnet':
        return HRNetV2(n_class=num_classes, pretrained=False, use_instance_norm=use_instance_norm)
    elif model_name.lower() == 'pspnet':
        return PSPNet(n_class=num_classes, backbone='resnet101', pretrained=False, use_instance_norm=use_instance_norm)
    elif model_name.lower() == 'transunet':
        return TransUNet(img_size=img_size, in_channels=3, out_channels=num_classes, 
                        use_instance_norm=use_instance_norm, pretrained=False)
    else:
        raise ValueError(f"Unknown model: {model_name}")


def calculate_metrics_batch(preds, targets, threshold=0.5):
    """Calculate metrics for a batch"""
    # Apply threshold
    preds_binary = (preds > threshold).float()
    
    # Flatten arrays
    preds_flat = preds_binary.view(-1).cpu().numpy()
    targets_flat = targets.view(-1).cpu().numpy()
    
    # Calculate pixel-wise metrics
    tp = np.sum((preds_flat == 1) & (targets_flat == 1))
    tn = np.sum((preds_flat == 0) & (targets_flat == 0))
    fp = np.sum((preds_flat == 1) & (targets_flat == 0))
    fn = np.sum((preds_flat == 0) & (targets_flat == 1))
    
    # Per-image IoU and Dice
    batch_size = preds.shape[0]
    ious = []
    dices = []
    
    for i in range(batch_size):
        pred_i = preds_binary[i].squeeze()
        target_i = targets[i].squeeze()
        
        intersection = (pred_i * target_i).sum()
        union = pred_i.sum() + target_i.sum() - intersection
        
        iou = (intersection + 1e-6) / (union + 1e-6)
        dice = (2 * intersection + 1e-6) / (pred_i.sum() + target_i.sum() + 1e-6)
        
        ious.append(iou.item())
        dices.append(dice.item())
    
    return {
        'tp': tp,
        'tn': tn,
        'fp': fp,
        'fn': fn,
        'ious': ious,
        'dices': dices
    }


def evaluate_model(model, test_loader, device, threshold=0.5, use_tta=False):
    """Evaluate model on test set"""
    model.eval()
    
    # Metrics storage
    all_metrics = {
        'tp': 0, 'tn': 0, 'fp': 0, 'fn': 0,
        'ious': [], 'dices': []
    }
    
    # Store predictions for visualization
    sample_predictions = []
    
    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(tqdm(test_loader, desc="Evaluating")):
            images = images.to(device)
            masks = masks.float().unsqueeze(1).to(device)
            
            # Forward pass
            if use_tta:
                preds = apply_tta(model, images, device)
            else:
                outputs = model(images)
                preds = torch.sigmoid(outputs)
            
            # Calculate metrics
            batch_metrics = calculate_metrics_batch(preds, masks, threshold)
            
            # Accumulate metrics
            all_metrics['tp'] += batch_metrics['tp']
            all_metrics['tn'] += batch_metrics['tn']
            all_metrics['fp'] += batch_metrics['fp']
            all_metrics['fn'] += batch_metrics['fn']
            all_metrics['ious'].extend(batch_metrics['ious'])
            all_metrics['dices'].extend(batch_metrics['dices'])
            
            # Save first few predictions for visualization
            if len(sample_predictions) < 20:
                for i in range(min(images.shape[0], 20 - len(sample_predictions))):
                    sample_predictions.append({
                        'image': images[i].cpu(),
                        'mask': masks[i].cpu(),
                        'pred': preds[i].cpu()
                    })
    
    # Calculate final metrics
    precision = all_metrics['tp'] / (all_metrics['tp'] + all_metrics['fp'] + 1e-6)
    recall = all_metrics['tp'] / (all_metrics['tp'] + all_metrics['fn'] + 1e-6)
    f1 = 2 * precision * recall / (precision + recall + 1e-6)
    accuracy = (all_metrics['tp'] + all_metrics['tn']) / (
        all_metrics['tp'] + all_metrics['tn'] + all_metrics['fp'] + all_metrics['fn'] + 1e-6
    )
    
    mean_iou = np.mean(all_metrics['ious'])
    mean_dice = np.mean(all_metrics['dices'])
    std_iou = np.std(all_metrics['ious'])
    std_dice = np.std(all_metrics['dices'])
    
    results = {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'accuracy': accuracy,
        'mean_iou': mean_iou,
        'std_iou': std_iou,
        'mean_dice': mean_dice,
        'std_dice': std_dice,
        'threshold': threshold
    }
    
    return results, sample_predictions


def save_visualizations(sample_predictions, output_dir, model_name):
    """Save visualization of predictions"""
    vis_dir = output_dir / 'visualizations'
    vis_dir.mkdir(exist_ok=True)
    
    # Create grid visualization
    n_samples = min(len(sample_predictions), 20)
    fig, axes = plt.subplots(n_samples, 3, figsize=(12, 4*n_samples))
    
    if n_samples == 1:
        axes = axes.reshape(1, -1)
    
    for idx, sample in enumerate(sample_predictions[:n_samples]):
        # Denormalize image
        image = sample['image'].permute(1, 2, 0).numpy()
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        image = std * image + mean
        image = np.clip(image, 0, 1)
        
        mask = sample['mask'].squeeze().numpy()
        pred = (sample['pred'].squeeze().numpy() > 0.5).astype(float)
        
        # Plot
        axes[idx, 0].imshow(image)
        axes[idx, 0].set_title('Input Image')
        axes[idx, 0].axis('off')
        
        axes[idx, 1].imshow(mask, cmap='gray')
        axes[idx, 1].set_title('Ground Truth')
        axes[idx, 1].axis('off')
        
        axes[idx, 2].imshow(pred, cmap='gray')
        axes[idx, 2].set_title('Prediction')
        axes[idx, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(vis_dir / f'{model_name}_predictions.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # Create IoU distribution plot
    plt.figure(figsize=(10, 6))
    plt.hist(sample_predictions, bins=50, alpha=0.7, color='blue', edgecolor='black')
    plt.xlabel('IoU Score')
    plt.ylabel('Frequency')
    plt.title(f'{model_name} - IoU Distribution on Test Set')
    plt.grid(True, alpha=0.3)
    plt.savefig(vis_dir / f'{model_name}_iou_distribution.png', dpi=150, bbox_inches='tight')
    plt.close()


def find_optimal_threshold(model, val_loader, device):
    """Find optimal threshold using validation set"""
    thresholds = np.arange(0.1, 0.9, 0.05)
    best_threshold = 0.5
    best_f1 = 0
    
    print("Finding optimal threshold...")
    for threshold in thresholds:
        results, _ = evaluate_model(model, val_loader, device, threshold)
        if results['f1'] > best_f1:
            best_f1 = results['f1']
            best_threshold = threshold
    
    print(f"Optimal threshold: {best_threshold:.2f} (F1: {best_f1:.4f})")
    return best_threshold


def main():
    parser = argparse.ArgumentParser(description='Evaluate trained segmentation model')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to saved model checkpoint')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to dataset')
    parser.add_argument('--model_name', type=str, required=True,
                       choices=['resunet', 'hrnet', 'pspnet', 'transunet'],
                       help='Model architecture')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='Output directory for results')
    parser.add_argument('--img_size', type=int, default=1024,
                       help='Image size')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size')
    parser.add_argument('--find_threshold', action='store_true',
                       help='Find optimal threshold on validation set')
    parser.add_argument('--use_tta', action='store_true',
                       help='Use Test Time Augmentation')
    parser.add_argument('--use_instance_norm', action='store_true', default=True,
                       help='Use Instance Normalization')
    
    args = parser.parse_args()
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Load model
    print(f"Loading {args.model_name} model...")
    model = get_model(args.model_name, img_size=args.img_size, use_instance_norm=args.use_instance_norm)
    checkpoint = torch.load(args.model_path, map_location=device, weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    # Create datasets
    transform = get_validation_augmentation(args.img_size)
    
    # Find optimal threshold if requested
    threshold = 0.5
    if args.find_threshold:
        val_dataset = SpheroidDataset(args.dataset_path, split='val', transform=transform)
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4
        )
        threshold = find_optimal_threshold(model, val_loader, device)
    
    # Evaluate on test set
    test_dataset = SpheroidDataset(args.dataset_path, split='test', transform=transform)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4
    )
    
    print(f"\nEvaluating on test set with threshold {threshold:.2f}...")
    if args.use_tta:
        print("Using Test Time Augmentation...")
    results, sample_predictions = evaluate_model(model, test_loader, device, threshold, use_tta=args.use_tta)
    
    # Print results
    print("\n" + "="*50)
    print(f"Results for {args.model_name}:")
    print("="*50)
    print(f"Accuracy:  {results['accuracy']:.4f}")
    print(f"Precision: {results['precision']:.4f}")
    print(f"Recall:    {results['recall']:.4f}")
    print(f"F1 Score:  {results['f1']:.4f}")
    print(f"Mean IoU:  {results['mean_iou']:.4f} ± {results['std_iou']:.4f}")
    print(f"Mean Dice: {results['mean_dice']:.4f} ± {results['std_dice']:.4f}")
    print("="*50)
    
    # Save results
    results_df = pd.DataFrame([results])
    results_df.to_csv(output_dir / f'{args.model_name}_results.csv', index=False)
    
    # Save visualizations
    save_visualizations(sample_predictions, output_dir, args.model_name)
    
    print(f"\nResults saved to {output_dir}")


if __name__ == '__main__':
    main()