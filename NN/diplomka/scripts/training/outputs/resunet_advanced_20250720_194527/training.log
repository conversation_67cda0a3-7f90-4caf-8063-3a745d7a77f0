Configuration:
- Model: Advanced ResUNet
- Dataset: /home/<USER>/spheroid_data/training_1024_all
- Batch Size: 8
- Learning Rate: 5e-4
- Optimizer: adamw
- Output Directory: scripts/training/outputs/resunet_advanced_20250720_194527

Checking model capacity...
Total parameters: 78,210,661
Model size: 298.35 MB
Input shape: torch.Size([1, 3, 256, 256])
Output shape: torch.Size([1, 1, 256, 256])
Model loaded successfully!

Starting training...
usage: CNN_main_spheroid.py [-h] --dataset_path DATASET_PATH
                            [--output_dir OUTPUT_DIR]
                            [--model {resunet,resunet_advanced,hrnet,pspnet,pspnet_regular,umamba}]
                            [--pretrained] [--epochs EPOCHS]
                            [--batch_size BATCH_SIZE] [--lr LR]
                            [--weight_decay WEIGHT_DECAY]
                            [--img_size IMG_SIZE]
                            [--focal_weight FOCAL_WEIGHT]
                            [--dice_weight DICE_WEIGHT]
                            [--iou_weight IOU_WEIGHT]
                            [--boundary_weight BOUNDARY_WEIGHT]
                            [--optimizer {adam,adamw,sgd}]
                            [--scheduler {reduce,cosine,onecycle}]
                            [--num_workers NUM_WORKERS] [--patience PATIENCE]
                            [--gpus GPUS] [--use_tta] [--use_instance_norm]
                            [--find_lr] [--min_delta MIN_DELTA] [--use_cache]
                            [--pretrained_path PRETRAINED_PATH]
                            [--freeze_backbone_epochs FREEZE_BACKBONE_EPOCHS]
CNN_main_spheroid.py: error: unrecognized arguments: --save_interval 5

Training failed!
