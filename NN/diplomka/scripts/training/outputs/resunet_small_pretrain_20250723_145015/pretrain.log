Pretraining started at: Wed Jul 23 02:50:15 PM CEST 2025
Configuration:
  Model: resunet_small
  Batch Size: 8
  Image Size: 1024
  Epochs: 150
  Learning Rate: 2e-4
  Weight Decay: 1e-4
  GPUs: 2

Starting pretraining...
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Epoch 0 Training:   0%|          | 0/1798 [00:00<?, ?it/s]
TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
[rank0]:[W723 14:50:57.522340931 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
[rank1]:[W723 14:50:58.828416346 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())

Epoch 0 Training:   0%|          | 0/1798 [00:31<?, ?it/s, loss=2.07, iou=0.0964, dice=0.176]
Epoch 0 Training:   0%|          | 1/1798 [00:31<15:39:54, 31.38s/it, loss=2.07, iou=0.0964, dice=0.176]
Epoch 0 Training:   0%|          | 1/1798 [00:32<15:39:54, 31.38s/it, loss=1.83, iou=0.166, dice=0.285] 
Epoch 0 Training:   0%|          | 2/1798 [00:32<6:50:10, 13.70s/it, loss=1.83, iou=0.166, dice=0.285] 
Epoch 0 Training:   0%|          | 2/1798 [00:33<6:50:10, 13.70s/it, loss=1.7, iou=0.166, dice=0.285] 
Epoch 0 Training:   0%|          | 3/1798 [00:33<3:58:46,  7.98s/it, loss=1.7, iou=0.166, dice=0.285]
Epoch 0 Training:   0%|          | 3/1798 [00:35<3:58:46,  7.98s/it, loss=1.73, iou=0.154, dice=0.268]
Epoch 0 Training:   0%|          | 4/1798 [00:35<2:38:07,  5.29s/it, loss=1.73, iou=0.154, dice=0.268]
Epoch 0 Training:   0%|          | 4/1798 [00:36<2:38:07,  5.29s/it, loss=1.72, iou=0.204, dice=0.338]
Epoch 0 Training:   0%|          | 5/1798 [00:36<1:53:33,  3.80s/it, loss=1.72, iou=0.204, dice=0.338]
Epoch 0 Training:   0%|          | 5/1798 [00:37<1:53:33,  3.80s/it, loss=1.65, iou=0.186, dice=0.314]
Epoch 0 Training:   0%|          | 6/1798 [00:37<1:26:42,  2.90s/it, loss=1.65, iou=0.186, dice=0.314]
Epoch 0 Training:   0%|          | 6/1798 [00:38<1:26:42,  2.90s/it, loss=1.45, iou=0.252, dice=0.403]
Epoch 0 Training:   0%|          | 7/1798 [00:38<1:09:36,  2.33s/it, loss=1.45, iou=0.252, dice=0.403]
Epoch 0 Training:   0%|          | 7/1798 [00:39<1:09:36,  2.33s/it, loss=1.41, iou=0.273, dice=0.429]
Epoch 0 Training:   0%|          | 8/1798 [00:39<58:27,  1.96s/it, loss=1.41, iou=0.273, dice=0.429]  
Epoch 0 Training:   0%|          | 8/1798 [00:40<58:27,  1.96s/it, loss=1.47, iou=0.243, dice=0.391]
Epoch 0 Training:   1%|          | 9/1798 [00:40<50:59,  1.71s/it, loss=1.47, iou=0.243, dice=0.391]
Epoch 0 Training:   1%|          | 9/1798 [00:42<50:59,  1.71s/it, loss=1.17, iou=0.409, dice=0.581]
Epoch 0 Training:   1%|          | 10/1798 [00:42<46:00,  1.54s/it, loss=1.17, iou=0.409, dice=0.581]
Epoch 0 Training:   1%|          | 10/1798 [00:43<46:00,  1.54s/it, loss=1.06, iou=0.426, dice=0.598]
Epoch 0 Training:   1%|          | 11/1798 [00:43<42:26,  1.43s/it, loss=1.06, iou=0.426, dice=0.598]
Epoch 0 Training:   1%|          | 11/1798 [00:44<42:26,  1.43s/it, loss=1.36, iou=0.298, dice=0.459]
Epoch 0 Training:   1%|          | 12/1798 [00:44<40:01,  1.34s/it, loss=1.36, iou=0.298, dice=0.459]
Epoch 0 Training:   1%|          | 12/1798 [00:45<40:01,  1.34s/it, loss=1.17, iou=0.388, dice=0.559]
Epoch 0 Training:   1%|          | 13/1798 [00:45<38:20,  1.29s/it, loss=1.17, iou=0.388, dice=0.559]
Epoch 0 Training:   1%|          | 13/1798 [00:46<38:20,  1.29s/it, loss=1.11, iou=0.391, dice=0.562]
Epoch 0 Training:   1%|          | 14/1798 [00:46<37:15,  1.25s/it, loss=1.11, iou=0.391, dice=0.562]
Epoch 0 Training:   1%|          | 14/1798 [00:47<37:15,  1.25s/it, loss=1.25, iou=0.357, dice=0.527]
Epoch 0 Training:   1%|          | 15/1798 [00:47<36:33,  1.23s/it, loss=1.25, iou=0.357, dice=0.527]
Epoch 0 Training:   1%|          | 15/1798 [00:49<36:33,  1.23s/it, loss=1.13, iou=0.353, dice=0.522]
Epoch 0 Training:   1%|          | 16/1798 [00:49<35:55,  1.21s/it, loss=1.13, iou=0.353, dice=0.522]
Epoch 0 Training:   1%|          | 16/1798 [00:50<35:55,  1.21s/it, loss=1.2, iou=0.364, dice=0.534] 
Epoch 0 Training:   1%|          | 17/1798 [00:50<35:25,  1.19s/it, loss=1.2, iou=0.364, dice=0.534]
Epoch 0 Training:   1%|          | 17/1798 [00:51<35:25,  1.19s/it, loss=1.3, iou=0.343, dice=0.511]
Epoch 0 Training:   1%|          | 18/1798 [00:51<35:05,  1.18s/it, loss=1.3, iou=0.343, dice=0.511]
Epoch 0 Training:   1%|          | 18/1798 [00:52<35:05,  1.18s/it, loss=0.883, iou=0.568, dice=0.725]
Epoch 0 Training:   1%|          | 19/1798 [00:52<34:53,  1.18s/it, loss=0.883, iou=0.568, dice=0.725]
Epoch 0 Training:   1%|          | 19/1798 [00:53<34:53,  1.18s/it, loss=1.11, iou=0.499, dice=0.666] 
Epoch 0 Training:   1%|          | 20/1798 [00:53<34:42,  1.17s/it, loss=1.11, iou=0.499, dice=0.666]
Epoch 0 Training:   1%|          | 20/1798 [00:54<34:42,  1.17s/it, loss=0.84, iou=0.609, dice=0.757]
Epoch 0 Training:   1%|          | 21/1798 [00:54<34:38,  1.17s/it, loss=0.84, iou=0.609, dice=0.757]
Epoch 0 Training:   1%|          | 21/1798 [00:55<34:38,  1.17s/it, loss=0.866, iou=0.663, dice=0.798]
Epoch 0 Training:   1%|          | 22/1798 [00:55<34:29,  1.17s/it, loss=0.866, iou=0.663, dice=0.798]
Epoch 0 Training:   1%|          | 22/1798 [00:57<34:29,  1.17s/it, loss=0.718, iou=0.725, dice=0.84] 
Epoch 0 Training:   1%|▏         | 23/1798 [00:57<34:25,  1.16s/it, loss=0.718, iou=0.725, dice=0.84]
Epoch 0 Training:   1%|▏         | 23/1798 [00:58<34:25,  1.16s/it, loss=1.01, iou=0.384, dice=0.555]
Epoch 0 Training:   1%|▏         | 24/1798 [00:58<34:18,  1.16s/it, loss=1.01, iou=0.384, dice=0.555]
Epoch 0 Training:   1%|▏         | 24/1798 [00:59<34:18,  1.16s/it, loss=0.949, iou=0.528, dice=0.691]
Epoch 0 Training:   1%|▏         | 25/1798 [00:59<34:14,  1.16s/it, loss=0.949, iou=0.528, dice=0.691]
Epoch 0 Training:   1%|▏         | 25/1798 [01:00<34:14,  1.16s/it, loss=1.24, iou=0.244, dice=0.392] 
Epoch 0 Training:   1%|▏         | 26/1798 [01:00<34:12,  1.16s/it, loss=1.24, iou=0.244, dice=0.392]
Epoch 0 Training:   1%|▏         | 26/1798 [01:01<34:12,  1.16s/it, loss=0.89, iou=0.443, dice=0.614]
Epoch 0 Training:   2%|▏         | 27/1798 [01:01<34:13,  1.16s/it, loss=0.89, iou=0.443, dice=0.614]
Epoch 0 Training:   2%|▏         | 27/1798 [01:02<34:13,  1.16s/it, loss=1.09, iou=0.282, dice=0.439]
Epoch 0 Training:   2%|▏         | 28/1798 [01:02<34:10,  1.16s/it, loss=1.09, iou=0.282, dice=0.439]
Epoch 0 Training:   2%|▏         | 28/1798 [01:04<34:10,  1.16s/it, loss=0.919, iou=0.453, dice=0.624]
Epoch 0 Training:   2%|▏         | 29/1798 [01:04<34:12,  1.16s/it, loss=0.919, iou=0.453, dice=0.624]
Epoch 0 Training:   2%|▏         | 29/1798 [01:05<34:12,  1.16s/it, loss=1.07, iou=0.312, dice=0.475] 
Epoch 0 Training:   2%|▏         | 30/1798 [01:05<34:08,  1.16s/it, loss=1.07, iou=0.312, dice=0.475]
Epoch 0 Training:   2%|▏         | 30/1798 [01:06<34:08,  1.16s/it, loss=1.03, iou=0.34, dice=0.508] 
Epoch 0 Training:   2%|▏         | 31/1798 [01:06<34:04,  1.16s/it, loss=1.03, iou=0.34, dice=0.508]
Epoch 0 Training:   2%|▏         | 31/1798 [01:07<34:04,  1.16s/it, loss=0.915, iou=0.508, dice=0.674]
Epoch 0 Training:   2%|▏         | 32/1798 [01:07<34:01,  1.16s/it, loss=0.915, iou=0.508, dice=0.674]
Epoch 0 Training:   2%|▏         | 32/1798 [01:08<34:01,  1.16s/it, loss=0.818, iou=0.568, dice=0.724]
Epoch 0 Training:   2%|▏         | 33/1798 [01:08<33:59,  1.16s/it, loss=0.818, iou=0.568, dice=0.724]
Epoch 0 Training:   2%|▏         | 33/1798 [01:09<33:59,  1.16s/it, loss=0.933, iou=0.596, dice=0.747]
Epoch 0 Training:   2%|▏         | 34/1798 [01:09<33:58,  1.16s/it, loss=0.933, iou=0.596, dice=0.747]
Epoch 0 Training:   2%|▏         | 34/1798 [01:10<33:58,  1.16s/it, loss=0.95, iou=0.544, dice=0.705] 
Epoch 0 Training:   2%|▏         | 35/1798 [01:10<33:58,  1.16s/it, loss=0.95, iou=0.544, dice=0.705]
Epoch 0 Training:   2%|▏         | 35/1798 [01:12<33:58,  1.16s/it, loss=1.03, iou=0.395, dice=0.566]
Epoch 0 Training:   2%|▏         | 36/1798 [01:12<33:55,  1.16s/it, loss=1.03, iou=0.395, dice=0.566]
Epoch 0 Training:   2%|▏         | 36/1798 [01:13<33:55,  1.16s/it, loss=0.732, iou=0.614, dice=0.761]
Epoch 0 Training:   2%|▏         | 37/1798 [01:13<33:54,  1.16s/it, loss=0.732, iou=0.614, dice=0.761]
Epoch 0 Training:   2%|▏         | 37/1798 [01:14<33:54,  1.16s/it, loss=0.755, iou=0.628, dice=0.771]
Epoch 0 Training:   2%|▏         | 38/1798 [01:14<33:52,  1.15s/it, loss=0.755, iou=0.628, dice=0.771]
Epoch 0 Training:   2%|▏         | 38/1798 [01:15<33:52,  1.15s/it, loss=0.765, iou=0.652, dice=0.789]
Epoch 0 Training:   2%|▏         | 39/1798 [01:15<33:50,  1.15s/it, loss=0.765, iou=0.652, dice=0.789]
Epoch 0 Training:   2%|▏         | 39/1798 [01:16<33:50,  1.15s/it, loss=0.89, iou=0.538, dice=0.699] 
Epoch 0 Training:   2%|▏         | 40/1798 [01:16<33:48,  1.15s/it, loss=0.89, iou=0.538, dice=0.699]
Epoch 0 Training:   2%|▏         | 40/1798 [01:17<33:48,  1.15s/it, loss=0.873, iou=0.567, dice=0.723]
Epoch 0 Training:   2%|▏         | 41/1798 [01:17<33:47,  1.15s/it, loss=0.873, iou=0.567, dice=0.723]
Epoch 0 Training:   2%|▏         | 41/1798 [01:19<33:47,  1.15s/it, loss=0.665, iou=0.696, dice=0.821]
Epoch 0 Training:   2%|▏         | 42/1798 [01:19<33:51,  1.16s/it, loss=0.665, iou=0.696, dice=0.821]