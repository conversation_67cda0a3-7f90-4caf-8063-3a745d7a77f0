#!/usr/bin/env python3
"""
Test script for ResUNet Small model
Verifies parameter count, memory usage, and 1024x1024 compatibility
"""

import torch
import torch.nn as nn
import sys
from pathlib import Path

# Add models directory to path
sys.path.append(str(Path(__file__).parent / "models"))

from resunet_small import ResUNetSmall

def count_parameters(model):
    """Count total and trainable parameters"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def get_model_size_mb(model):
    """Calculate model size in MB"""
    total_params = sum(p.numel() for p in model.parameters())
    return total_params * 4 / 1024 / 1024  # 4 bytes per float32

def test_forward_pass(model, input_size=(1, 3, 1024, 1024)):
    """Test forward pass with given input size"""
    model.eval()
    with torch.no_grad():
        x = torch.randn(input_size)
        try:
            output = model(x)
            if isinstance(output, tuple):
                output = output[0]
            return True, output.shape
        except Exception as e:
            return False, str(e)

def test_memory_usage(model, batch_sizes=[1, 2, 4, 8, 12, 16]):
    """Test memory usage with different batch sizes"""
    model.eval()
    results = {}
    
    for batch_size in batch_sizes:
        try:
            torch.cuda.empty_cache()
            x = torch.randn(batch_size, 3, 1024, 1024)
            
            if torch.cuda.is_available():
                model = model.cuda()
                x = x.cuda()
                
                # Measure memory before forward pass
                torch.cuda.synchronize()
                memory_before = torch.cuda.memory_allocated()
                
                with torch.no_grad():
                    output = model(x)
                
                torch.cuda.synchronize()
                memory_after = torch.cuda.memory_allocated()
                memory_used = (memory_after - memory_before) / 1024 / 1024  # MB
                
                results[batch_size] = {
                    'success': True,
                    'memory_mb': memory_used,
                    'output_shape': output.shape if not isinstance(output, tuple) else output[0].shape
                }
                
                model = model.cpu()
            else:
                with torch.no_grad():
                    output = model(x)
                results[batch_size] = {
                    'success': True,
                    'memory_mb': 'N/A (CPU)',
                    'output_shape': output.shape if not isinstance(output, tuple) else output[0].shape
                }
                
        except Exception as e:
            results[batch_size] = {
                'success': False,
                'error': str(e)
            }
    
    return results

def main():
    print("=" * 80)
    print("ResUNet Small Model Test")
    print("=" * 80)
    
    # Test different configurations
    configs = [
        {"name": "Default", "kwargs": {}},
        {"name": "With Deep Supervision", "kwargs": {"use_deep_supervision": True}},
        {"name": "Higher Dropout", "kwargs": {"dropout_rate": 0.2}},
        {"name": "No Spatial Dropout", "kwargs": {"use_spatial_dropout": False}},
    ]
    
    for config in configs:
        print(f"\n{'-' * 60}")
        print(f"Testing: {config['name']}")
        print(f"{'-' * 60}")
        
        try:
            # Create model
            model = ResUNetSmall(**config['kwargs'])
            
            # Count parameters
            total_params, trainable_params = count_parameters(model)
            model_size_mb = get_model_size_mb(model)
            
            print(f"Total parameters: {total_params:,}")
            print(f"Trainable parameters: {trainable_params:,}")
            print(f"Model size: {model_size_mb:.1f} MB")
            
            # Check if close to target (60M parameters)
            target_params = 60_000_000
            param_ratio = total_params / target_params
            print(f"Parameter ratio vs 60M target: {param_ratio:.2f}")
            
            if 0.8 <= param_ratio <= 1.2:
                print("✅ Parameter count within target range (48M-72M)")
            else:
                print("⚠️  Parameter count outside target range")
            
            # Test forward pass
            print(f"\nTesting forward pass...")
            success, result = test_forward_pass(model)
            if success:
                print(f"✅ Forward pass successful: {result}")
            else:
                print(f"❌ Forward pass failed: {result}")
                continue
            
            # Test different input sizes
            test_sizes = [
                (1, 3, 512, 512),
                (1, 3, 1024, 1024),
                (2, 3, 1024, 1024),
            ]
            
            print(f"\nTesting different input sizes:")
            for size in test_sizes:
                success, result = test_forward_pass(model, size)
                if success:
                    print(f"  {size} -> {result} ✅")
                else:
                    print(f"  {size} -> Failed: {result} ❌")
            
            # Test memory usage if CUDA available
            if torch.cuda.is_available():
                print(f"\nTesting memory usage (CUDA):")
                memory_results = test_memory_usage(model, [1, 2, 4, 8, 12])
                
                for batch_size, result in memory_results.items():
                    if result['success']:
                        print(f"  Batch {batch_size}: {result['memory_mb']:.1f} MB")
                    else:
                        print(f"  Batch {batch_size}: Failed - {result['error']}")
            else:
                print(f"\nCUDA not available, skipping memory tests")
            
        except Exception as e:
            print(f"❌ Model creation failed: {e}")
    
    # Compare with other models
    print(f"\n{'=' * 80}")
    print("Model Comparison")
    print(f"{'=' * 80}")
    
    try:
        # Import other models for comparison
        sys.path.append("models")
        from resunet import ResUNet
        from resunet_optimized import OptimizedResUNet
        
        models_to_compare = [
            ("ResUNet (Original)", ResUNet, {}),
            ("ResUNet Small", ResUNetSmall, {}),
            ("ResUNet Optimized", OptimizedResUNet, {}),
        ]
        
        print(f"{'Model':<20} {'Parameters':<15} {'Size (MB)':<12} {'1024x1024':<12}")
        print("-" * 60)
        
        for name, model_class, kwargs in models_to_compare:
            try:
                model = model_class(**kwargs)
                total_params, _ = count_parameters(model)
                size_mb = get_model_size_mb(model)
                
                # Test 1024x1024 compatibility
                success, _ = test_forward_pass(model, (1, 3, 1024, 1024))
                compat = "✅" if success else "❌"
                
                print(f"{name:<20} {total_params:<15,} {size_mb:<12.1f} {compat:<12}")
                
            except Exception as e:
                print(f"{name:<20} {'Error':<15} {'Error':<12} {'Error':<12}")
    
    except ImportError:
        print("Could not import other models for comparison")
    
    print(f"\n{'=' * 80}")
    print("Test completed!")
    print(f"{'=' * 80}")

if __name__ == "__main__":
    main()
